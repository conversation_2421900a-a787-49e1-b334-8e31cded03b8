@import "tailwindcss";

:root {
  /* Theme Colors */
  --primary-50: #fff7ed;
  --primary-100: #ffedd5;
  --primary-200: #fed7aa;
  --primary-300: #fdba74;
  --primary-400: #fb923c;
  --primary-500: #f97316;
  --primary-600: #ea580c;
  --primary-700: #c2410c;
  --primary-800: #9a3412;
  --primary-900: #7c2d12;

  --secondary-50: #fefce8;
  --secondary-100: #fef9c3;
  --secondary-200: #fef08a;
  --secondary-300: #fde047;
  --secondary-400: #facc15;
  --secondary-500: #eab308;
  --secondary-600: #ca8a04;
  --secondary-700: #a16207;
  --secondary-800: #854d0e;
  --secondary-900: #713f12;

  --accent-50: #fdf4ff;
  --accent-100: #fae8ff;
  --accent-200: #f5d0fe;
  --accent-300: #f0abfc;
  --accent-400: #e879f9;
  --accent-500: #d946ef;
  --accent-600: #c026d3;
  --accent-700: #a21caf;
  --accent-800: #86198f;
  --accent-900: #701a75;

  /* Background Colors */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-glass: rgba(255, 255, 255, 0.1);
  --bg-glass-dark: rgba(0, 0, 0, 0.3);

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;
  --text-accent: var(--primary-300);

  /* Border Colors */
  --border-primary: rgba(255, 255, 255, 0.1);
  --border-secondary: rgba(255, 255, 255, 0.05);
  --border-accent: var(--primary-500);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  --gradient-secondary: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  --gradient-accent: linear-gradient(135deg, var(--accent-500), var(--primary-500));
  --gradient-header: linear-gradient(90deg, var(--primary-500), var(--secondary-500), var(--primary-500));
  --gradient-orange-underline: linear-gradient(90deg, #fb923c, #f97316, #ea580c);
}

@theme inline {
  --color-background: var(--bg-primary);
  --color-foreground: var(--text-primary);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom utility classes */
.bg-theme-primary { background-color: var(--bg-primary); }
.bg-theme-secondary { background-color: var(--bg-secondary); }
.bg-theme-tertiary { background-color: var(--bg-tertiary); }
.bg-theme-glass { background-color: var(--bg-glass); }

.text-theme-primary { color: var(--text-primary); }
.text-theme-secondary { color: var(--text-secondary); }
.text-theme-muted { color: var(--text-muted); }
.text-theme-accent { color: var(--text-accent); }

.border-theme-primary { border-color: var(--border-primary); }
.border-theme-secondary { border-color: var(--border-secondary); }
.border-theme-accent { border-color: var(--border-accent); }

.gradient-primary { background: var(--gradient-primary); }
.gradient-secondary { background: var(--gradient-secondary); }
.gradient-accent { background: var(--gradient-accent); }
.gradient-header { background: var(--gradient-header); }
.gradient-orange-underline { background: var(--gradient-orange-underline); }
