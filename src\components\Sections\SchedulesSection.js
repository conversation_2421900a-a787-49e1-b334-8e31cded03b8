import React, { useState } from 'react';
import Container from '../Layout/Container';
import useResponsive from '../../hooks/useResponsive';

const SchedulesSection = () => {
  const { isMobile } = useResponsive();
  const [activeDay, setActiveDay] = useState(1);

  const scheduleData = {
    1: [
      {
        time: '08:00 - 09:00',
        title: 'Registration & Welcome Coffee',
        speaker: '',
        type: 'Registration'
      },
      {
        time: '09:00 - 09:30',
        title: 'Opening Ceremony',
        speaker: 'Event Host',
        type: 'Ceremony'
      },
      {
        time: '09:30 - 10:30',
        title: 'Keynote: Shaping Tomorrow\'s Workplace',
        speaker: 'Keynote Speaker',
        type: 'Keynote'
      },
      {
        time: '10:30 - 11:00',
        title: 'Networking Break',
        speaker: '',
        type: 'Break'
      },
      {
        time: '11:00 - 12:00',
        title: 'Panel Discussion: Future of Work',
        speaker: 'Industry Leaders',
        type: 'Panel'
      },
      {
        time: '12:00 - 13:30',
        title: 'Lunch & Networking',
        speaker: '',
        type: 'Break'
      },
      {
        time: '13:30 - 14:30',
        title: 'Workshop: Leadership in Digital Age',
        speaker: 'Workshop Leader',
        type: 'Workshop'
      },
      {
        time: '14:30 - 15:00',
        title: 'Coffee Break',
        speaker: '',
        type: 'Break'
      },
      {
        time: '15:00 - 16:00',
        title: 'Fireside Chat: Innovation & Growth',
        speaker: 'Special Guest',
        type: 'Talk'
      },
      {
        time: '16:00 - 17:00',
        title: 'Day 1 Closing & Networking',
        speaker: '',
        type: 'Networking'
      }
    ],
    2: [
      {
        time: '08:30 - 09:00',
        title: 'Day 2 Registration & Coffee',
        speaker: '',
        type: 'Registration'
      },
      {
        time: '09:00 - 10:00',
        title: 'Morning Keynote: Building Resilient Teams',
        speaker: 'Keynote Speaker',
        type: 'Keynote'
      },
      {
        time: '10:00 - 10:30',
        title: 'Networking Break',
        speaker: '',
        type: 'Break'
      },
      {
        time: '10:30 - 11:30',
        title: 'Masterclass Sessions (Parallel)',
        speaker: 'Various Experts',
        type: 'Masterclass'
      },
      {
        time: '11:30 - 12:30',
        title: 'Panel: Workplace Transformation',
        speaker: 'Industry Experts',
        type: 'Panel'
      },
      {
        time: '12:30 - 14:00',
        title: 'Lunch & Awards Ceremony',
        speaker: '',
        type: 'Ceremony'
      },
      {
        time: '14:00 - 15:00',
        title: 'Closing Keynote: The Blessed Workplace',
        speaker: 'Closing Speaker',
        type: 'Keynote'
      },
      {
        time: '15:00 - 15:30',
        title: 'Closing Ceremony & Next Steps',
        speaker: 'Event Host',
        type: 'Ceremony'
      },
      {
        time: '15:30 - 16:30',
        title: 'Final Networking & Farewell',
        speaker: '',
        type: 'Networking'
      }
    ]
  };

  const getTypeColor = (type) => {
    const colors = {
      'Registration': 'bg-blue-600 text-white',
      'Ceremony': 'bg-purple-600 text-white',
      'Keynote': 'bg-orange-600 text-white',
      'Break': 'bg-gray-600 text-white',
      'Panel': 'bg-green-600 text-white',
      'Workshop': 'bg-amber-600 text-white',
      'Talk': 'bg-pink-600 text-white',
      'Networking': 'bg-indigo-600 text-white',
      'Masterclass': 'bg-red-600 text-white'
    };
    return colors[type] || 'bg-gray-600 text-white';
  };

  return (
    <section id="schedules" className="min-h-screen bg-black py-16 lg:py-24">
      <Container>
        {/* Section Header */}
        <div className="text-center mb-12 lg:mb-16">
          <h2 className={`${isMobile ? 'text-3xl' : 'text-4xl lg:text-5xl'} font-bold mb-4 text-white`}>
            Schedules 日程表
          </h2>
        </div>

        {/* Day Selector */}
        <div className="flex justify-center mb-8 lg:mb-12">
          <div className="flex space-x-2">
            <button
              onClick={() => setActiveDay(1)}
              className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                activeDay === 1
                  ? 'bg-orange-500 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Day 1
              <div className="text-xs mt-1">22 AUG</div>
            </button>
            <button
              onClick={() => setActiveDay(2)}
              className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                activeDay === 2
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Day 2
              <div className="text-xs mt-1">23 AUG</div>
            </button>
          </div>
        </div>

        {/* Schedule Timeline */}
        <div className="max-w-4xl mx-auto">
          <div className="space-y-4">
            {scheduleData[activeDay].map((item, index) => (
              <div
                key={index}
                className="bg-gray-800 rounded-lg p-6 hover:bg-gray-700 transition-colors duration-300"
              >
                <div className="flex items-center space-x-6">
                  {/* Time */}
                  <div className="flex-shrink-0 w-32">
                    <div className="text-orange-400 font-bold text-lg">{item.time}</div>
                  </div>

                  {/* Type Badge */}
                  <div className="flex-shrink-0">
                    <span className={`px-3 py-1 rounded-md text-xs font-medium ${getTypeColor(item.type)}`}>
                      {item.type}
                    </span>
                  </div>

                  {/* Content */}
                  <div className="flex-grow">
                    <h3 className="text-white font-semibold text-lg mb-1">{item.title}</h3>
                    {item.speaker && (
                      <p className="text-gray-400 text-sm">{item.speaker}</p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
};

export default SchedulesSection;
